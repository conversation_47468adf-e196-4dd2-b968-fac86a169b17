import {authenticate} from '@loopback/authentication';
import {
  UserCredentialsRepository,
  UserRepository
} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {UserProfile as LibUserProfile, SecurityBindings, securityId} from '@loopback/security';
import {LcaDataSubmission} from '../models';
import {LcaDataSubmissionRepository, UserProfileRepository, VendorCodeRepository} from '../repositories';

@authenticate('cognito-tvs', 'jwt')
export class LcaDataSubmissionController {
  constructor(
    @repository(LcaDataSubmissionRepository)
    public lcaDataSubmissionRepository: LcaDataSubmissionRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(VendorCodeRepository)
    public vendorCodeRepository: VendorCodeRepository,
    @repository(UserCredentialsRepository) protected userCredentialsRepository: UserCredentialsRepository,
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @post('/lca-data-submissions')
  @response(200, {
    description: 'LcaDataSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(LcaDataSubmission)}},
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LcaDataSubmission, {
            title: 'NewLcaDataSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    lcaDataSubmission: Omit<LcaDataSubmission, 'id'>,
  ): Promise<LcaDataSubmission> {
    return this.lcaDataSubmissionRepository.create(lcaDataSubmission);
  }

  @get('/lca-data-submissions/count')
  @response(200, {
    description: 'LcaDataSubmission model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(LcaDataSubmission) where?: Where<LcaDataSubmission>,
  ): Promise<Count> {
    return this.lcaDataSubmissionRepository.count(where);
  }

  @get('/lca-data-submissions')
  @response(200, {
    description: 'Array of LcaDataSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(LcaDataSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @param.filter(LcaDataSubmission) filter?: Filter<LcaDataSubmission>,
  ): Promise<LcaDataSubmission[]> {

    return await this.lcaDataSubmissionRepository.find(filter);
    ;
  }

  @patch('/lca-data-submissions')
  @response(200, {
    description: 'LcaDataSubmission PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LcaDataSubmission, {partial: true}),
        },
      },
    })
    lcaDataSubmission: LcaDataSubmission,
    @param.where(LcaDataSubmission) where?: Where<LcaDataSubmission>,
  ): Promise<Count> {
    return this.lcaDataSubmissionRepository.updateAll(lcaDataSubmission, where);
  }

  @get('/lca-data-submissions/{id}')
  @response(200, {
    description: 'LcaDataSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LcaDataSubmission, {includeRelations: true}),
      },
    },
  })
  async findById(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @param.path.number('id') id: number,
    @param.filter(LcaDataSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<LcaDataSubmission>
  ): Promise<LcaDataSubmission> {
    return this.lcaDataSubmissionRepository.findById(id, filter);
  }

  @patch('/lca-data-submissions/{id}')
  @response(204, {
    description: 'LcaDataSubmission PATCH success',
  })
  async updateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LcaDataSubmission, {partial: true}),
        },
      },
    })
    lcaDataSubmission: LcaDataSubmission,
  ): Promise<void> {
    const userId = currentUserProfile[securityId];
    const userDetail = await this.userRepository.findById(userId);
    const userProfileDetail = await this.userProfileRepository.findOne({
      where: {userId: userId}, include: [
        {
          relation: 'vendorCodes'
        },
      ], limit: 1
    });

    const supplierId = lcaDataSubmission.supplierId;
    const vendorCode = lcaDataSubmission.vendorCode;
    const vendorId = lcaDataSubmission.vendorId;
    console.log(userId, userProfileDetail?.id)

    if (!userProfileDetail || (supplierId ? userProfileDetail.id !== supplierId : false)) {
      throw new HttpErrors.Forbidden('You are not authorized to access this resource 1');
    }

    const match = userProfileDetail?.vendorCodes?.some(vc =>
      vc.code === vendorCode
    )

    if (!match) {
      throw new HttpErrors.Forbidden('You are not authorized to access this resource 2');
    }
    await this.lcaDataSubmissionRepository.updateById(id, lcaDataSubmission);
  }
  @patch('/lca-data-submissions-update-custom/{id}')
  @response(204, {
    description: 'LcaDataSubmission PATCH success',
  })
  async updateByIdByOthers(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LcaDataSubmission, {partial: true}),
        },
      },
    })
    lcaDataSubmission: LcaDataSubmission,
  ): Promise<void> {

    await this.lcaDataSubmissionRepository.updateById(id, lcaDataSubmission);
  }
  @put('/lca-data-submissions/{id}')
  @response(204, {
    description: 'LcaDataSubmission PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() lcaDataSubmission: LcaDataSubmission,
  ): Promise<void> {
    await this.lcaDataSubmissionRepository.replaceById(id, lcaDataSubmission);
  }

  @del('/lca-data-submissions/{id}')
  @response(204, {
    description: 'LcaDataSubmission DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.lcaDataSubmissionRepository.deleteById(id);
  }

  // Unified endpoint to handle both POST and PATCH
  @post('/lca-data-submissions/save')
  @response(200, {
    description: 'Create or Update LcaDataSubmission',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: getModelSchemaRef(LcaDataSubmission),
          },
        },
      },
    },
  })
  async saveOrUpdate(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['partNumber'],
            properties: {
              id: {
                type: 'number',
                description: 'Optional: If provided, will update existing record',
              },
              partNumber: {
                type: 'string',
                description: 'Part number must be unique across table',
              },
              vendorId: {
                type: 'number',
                description: 'Optional: Vendor ID',
              },
              supplierId: {type: 'number'},
              vendorCode: {type: 'string'},
              description: {type: 'string'},
              imdsId: {type: 'object'},
              lastUpdateDate: {type: 'string'},
              dataCollectionStatus: {type: 'object'},
              status: {type: 'string'},
              lock: {type: 'boolean'},
              reject: {type: 'number'},
              data: {type: 'string'},
              completed_stage: {type: 'array', items: {type: 'object'}},
              process: {type: 'array', items: {type: 'string'}},
              created_on: {
                type: 'string',
                description: 'Optional: Creation timestamp',
              },
              created_by: {
                type: 'number',
                description: 'Optional: User who created the record',
              },
              modified_on: {
                type: 'string',
                description: 'Optional: Last modification timestamp',
              },
              modified_by: {
                type: 'number',
                description: 'Optional: User who last modified the record',
              },
            },
          },
        },
      },
    })
    requestData: {
      id?: number;
      partNumber: string;
      vendorId?: number;
      supplierId?: number;
      vendorCode?: string;
      description?: string;
      imdsId?: any;
      lastUpdateDate?: string;
      dataCollectionStatus?: any;
      status?: string;
      lock?: boolean;
      reject?: number;
      data?: string;
      completed_stage?: any[];
      process?: string[];
      created_on?: string;
      created_by?: number;
      modified_on?: string;
      modified_by?: number;
    },
  ): Promise<{status: boolean; message: string; data?: LcaDataSubmission}> {
    try {
      const {id, partNumber, vendorId, created_on, created_by, modified_on, modified_by, ...otherFields} = requestData;

      // Check if partNumber is unique across entire table (excluding current record if updating)
      const existingPartNumber = await this.lcaDataSubmissionRepository.findOne({
        where: {
          partNumber: partNumber,
          ...(id && {id: {neq: id}}), // Exclude current record if updating
        },
      });

      if (existingPartNumber) {
        return {
          status: false,
          message: `Part number "${partNumber}" already exists in the system`,
        };
      }

      if (id) {
        // PATCH operation - Update existing record
        const existingRecord = await this.lcaDataSubmissionRepository.findById(id);
        if (!existingRecord) {
          return {
            status: false,
            message: 'Record not found for update',
          };
        }

        // Prepare update data (ignore created_on and created_by unless explicitly provided)
        const updateData: Partial<LcaDataSubmission> = {
          partNumber: partNumber,
          ...(vendorId && {vendorId: vendorId}),

          modified_on: modified_on || new Date().toISOString(),
          ...(modified_by && {modified_by: modified_by}),
        };

        await this.lcaDataSubmissionRepository.updateById(id, updateData);

        // Fetch updated record
        const updatedRecord = await this.lcaDataSubmissionRepository.findById(id);

        return {
          status: true,
          message: 'LCA data submission updated successfully',
          data: updatedRecord,
        };
      } else {
        // POST operation - Create new record
        const newRecord = await this.lcaDataSubmissionRepository.create({
          partNumber: partNumber,
          ...(vendorId && {vendorId: vendorId}),

          created_on: created_on || new Date().toISOString(),
          ...(created_by && {created_by: created_by}),
        });

        return {
          status: true,
          message: 'LCA data submission created successfully',
          data: newRecord,
        };
      }
    } catch (error) {
      console.error('Error in saveOrUpdate:', error);
      return {
        status: false,
        message: 'Failed to save LCA data submission. Please contact support.',
      };
    }
  }

  @post('/lca-data-submissions/update-vendor-ids')
  @response(200, {
    description: 'Update vendor IDs in LCA data submissions',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            rejection_data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  vendorCode: {type: 'string'},
                  partNumber: {type: 'string'},
                  message: {type: 'string'}
                }
              }
            }
          }
        }
      }
    }
  })
  async updateVendorIds(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                vendorCode: {type: 'string'},
                partNumber: {type: 'string'}
              }
            }
          }
        }
      }
    }) requestData: Array<{vendorCode: any; partNumber: any}>
  ): Promise<{status: boolean; message: string; rejection_data: Array<{vendorCode: string; partNumber: string; message: string}>}> {
    try {
      const rejectionData: Array<{vendorCode: string; partNumber: string; message: string}> = [];
      let successCount = 0;

      // Process each item in the array
      for (const item of requestData) {
        const {vendorCode, partNumber} = item;

        // Check if both vendorCode and partNumber are strings
        if (typeof vendorCode !== 'string' || typeof partNumber !== 'string') {
          rejectionData.push({
            vendorCode: String(vendorCode || ''),
            partNumber: String(partNumber || ''),
            message: 'Both vendorCode and partNumber must be strings'
          });
          continue;
        }

        try {
          // Find vendor by code
          const vendor = await this.vendorCodeRepository.findOne({
            where: {code: vendorCode}
          });

          if (!vendor) {
            rejectionData.push({
              vendorCode,
              partNumber,
              message: `Vendor not found for code: ${vendorCode}`
            });
            continue;
          }

          // Find LCA data submission by partNumber where data is null/empty
          const lcaSubmission = await this.lcaDataSubmissionRepository.findOne({
            where: {
              partNumber: partNumber,
            }
          });

          // Check if submission exists and data is null/empty
          if (!lcaSubmission) {
            rejectionData.push({
              vendorCode,
              partNumber,
              message: `LCA data submission not found for part number: ${partNumber}`
            });
            continue;
          }

          // Check if data is not null/empty (should be null for update)
          if (lcaSubmission.data != null) {
            rejectionData.push({
              vendorCode,
              partNumber,
              message: `LCA data submission for part number: ${partNumber} already has data (not null)`
            });
            continue;
          }

          // Prepare the update data with all required properties
          const updateData = {
            supplierId: vendor.userProfileId,
            vendorCode: vendor.code,
            vendorId: vendor.id,
            description: "",
            imdsId: "",
            dataCollectionStatus: [
              {
                "name": "Process",
                "status": "Not Stared",
                "data": null
              },
              {
                "name": "Energy Consumption",
                "status": "Not Stared",
                "data": null
              },
              {
                "name": "Special Process Material",
                "status": "Not Stared",
                "data": null
              },
              {
                "name": "Waste",
                "status": "Not Stared",
                "data": null
              },
              {
                "name": "Transportation",
                "status": "Not Stared",
                "data": null
              }
            ],
            data: "[{\"categoryType\":\"process\",\"files\":[],\"categories\":[{\"name\":\"Manufacturing Process (Assembly / Forging / Casting / Stamping)\",\"details\":[{\"detail\":\"\",\"remarks\":\"\"}]},{\"name\":\"Special Process (Heat Treatment / Plating / Coating)\",\"details\":[{\"detail\":\"\",\"remarks\":\"\"}]}]},{\"categoryType\":\"energy\",\"categories\":[{\"name\":\"Electricity\",\"details\":[{\"type\":\"\",\"units (in Kilowatt-hours)\":null,\"remarks\":\"\"}]},{\"name\":\"Fuel\",\"details\":[{\"type\":\"\",\"units (in Liters)\":null,\"remarks\":\"\"}]},{\"name\":\"Gas\",\"details\":[{\"type\":\"\",\"units (in Cubic meters)\":null,\"remarks\":\"\"}]}]},{\"categoryType\":\"specialProcessMaterial\",\"categories\":[{\"name\":\"Utilities & Auxiliaries (Water / Coolant / Lubricant / Paint / Plating)\",\"details\":[{\"nameOrGrade\":\"\",\"quantity\":null,\"unit of measure\":\"\",\"remarks\":\"\"}]}]},{\"categoryType\":\"waste\",\"categories\":[{\"name\":\"Air Emission\",\"details\":[{\"materialName\":\"\",\"quantity (in milligrams per cubic meter)\":null,\"remarks\":\"\"}]},{\"name\":\"Water Emission\",\"details\":[{\"materialName\":\"\",\"quantity (in milligrams per liter)\":null,\"remarks\":\"\"}]},{\"name\":\"Solid Waste\",\"details\":[{\"materialName\":\"\",\"quantity (in Kilogram)\":null,\"remarks\":\"\"}]}]},{\"categoryType\":\"transportation\",\"categories\":[{\"name\":\"Transportation\",\"details\":[{\"mode\":\"\",\"from (Manufacturing Location)\":\"\",\"to (TVS Motor Plant)\":\"\",\"distance\":null,\"unit of Measure\":\"\",\"remarks\":\"\"}]}]}]",
            completed_stage: [],
            process: [
              "Process",
              "Energy Consumption",
              "Special Process Material",
              "Waste",
              "Transportation"
            ],
            lock: false
          };

          // Update the LCA data submission with all properties
          await this.lcaDataSubmissionRepository.updateById(lcaSubmission.id, updateData);

          successCount++;

        } catch (itemError) {
          console.error(`Error processing item ${vendorCode}-${partNumber}:`, itemError);
          rejectionData.push({
            vendorCode,
            partNumber,
            message: `Error processing item: ${itemError.message || 'Unknown error'}`
          });
        }
      }

      return {
        status: true,
        message: `Successfully updated ${successCount} records. ${rejectionData.length} items were rejected.`,
        rejection_data: rejectionData
      };

    } catch (error) {
      console.error('Error in updateVendorIds:', error);
      return {
        status: false,
        message: 'Failed to update vendor IDs. Please contact support.',
        rejection_data: []
      };
    }
  }
}
